{
  // ========== 主题和界面设置 ==========
  "workbench.colorTheme": "One Dark Modern Classic",
  "workbench.editor.autoLockGroups": {
    "workbench.editor.chatSession": false
  },

  // ========== Augment 扩展配置 ==========
  "augment.chat.userGuidelines": "@结合上下文，@相关上下文：你是一个具备记忆的大型超级智能体。\n\n始终输出中文\n\n刚才你提到，使用简单的方案，使用替代方案，我们不使用替代方案，也不使用简单方案，使用完整的方案和最高级的方案。哪里出了问题停下来解决。\n\n禁止创建和生成.bat和.cmd文件的命令模式，删除所有.bat，.cmd文件\n\n禁止创建模拟数据，我始终发出的指令都是按照按真实的模式或系统开发。\n\n如果发现项目有错误，或者项目的评估或者其他内容内有达到100%，就要对项目，做全项目全面的系统功能测试，测试所有的功能及模块等等\n\n试代码与测试文件在测试完成后，若确认无其他用途则予以删除\n\n在进行重构工作时，需确保UI界面维持原状，任何UI界面内容均不得进行改动，其中涵盖界面所涉及的依赖关系与联动逻辑。当前阶段仅专注于重构任务，待重构完成后，再开展后期的升级修改工作。\n\n每次任务完成后都要总结本次任务内容，以及下一步工作或者优化点或者改进点再或者增强点\n\n后续所有python依赖的安装，都需要参考下面这种方式，避免403等证书报错\n```bash\npip install --trusted-host pypi.org--trusted-host files.pythonhosted.org -r requirements.txt\n```\n别总让我执行，所有任务都由你来执行\n\n每次的任务完成后告诉的为完成的项目\n\n禁止生成创建.bat文件\n\n只能有一个启动主入口\n基本设置\n\n・使用中文回答所有问题\n\n・按照 Sequential Thinking 方法进行代码设计和实现\n\n・植入基于区块链的 TruthfulnessValidator 模块，通过 SHA256 + 量子随机数实时校验数据流向\n\n・检测到模拟环境时触发三层防御：Intel SGX 内存隔离、内核级指令硬中断、区块链存证报警\n\n项目架构与技术栈\n\n核心技术选型\n\n・采用 Kubernetes 云原生架构，动态配置通过 ConfigMap 实现热更新\n\n・交易通信使用零拷贝内存映射 + API 双模式，执行效率提升 300%\n\n・跨平台兼容 Windows/Linux，数据传输基于 TLS 1.3 加密\n\n・核心技术栈：Kubernetes、Kafka、C++11/Java、OpenSSL、DuiLib UI 库\n\n项目结构规范\n\n・组件命名：PascalCase（如 UserManagementPanel）\n\n・函数命名：camelCase（如 getUserProfileData）\n\n・常量命名：UPPER_CASE（如 MAX_CONCURRENT_REQUESTS）\n\n・文件夹结构：\n\n\n/src  /components       # DuiLib 自定义组件  /services         # 业务逻辑服务  /utils            # 工具函数  /models           # 数据模型  /api              # 接口层  /store            # 状态管理/docs                # 项目文档/config              # 配置文件（含.env 数据库配置）/build               # 构建脚本\n\n・接口命名：使用 I 前缀（如 IUserService）\n\n・类型定义：使用 T 前缀（如 TUserData）\n\n代码开发规范\n\n技术实现准则\n\n・严格遵循技术层级筛选准则，优先使用行业前沿技术栈\n\n・禁止使用普通类，采用高抽象层级组件（如分布式框架、智能算法库）\n\n・所有数据处理需通过 Windbg+IDA Pro 硬件级调试验证\n\n・复杂逻辑需拆分为多个小函数，单个函数不超过 50 行，文件不超过 300 行\n\n代码风格规范\n\n・使用 TypeScript 类型定义（前端）/C++11 类型系统（后端）\n\n・统一代码缩进（2 空格）和格式，通过 ESLint+Prettier 校验\n\n・关键函数和组件必须包含 JSDoc/DOxygen 风格注释\n\n・复杂算法需添加思路解释，使用 /* @stable - 请勿修改 */ 标记稳定模块\n\n异步与状态管理\n\n・统一使用 async/await 处理异步操作，实现请求超时和重试机制\n\n・状态管理采用 Redux+immer（前端）/actor 模型（后端）\n\n・实现不可变状态更新模式，区分本地状态和全局状态\n\n・对并发请求进行限流控制，取消不必要的请求释放资源\n\n开发流程与协作规范\n\n标准开发流程\n\n1.问题分析：通过区块链存证明确任务目标和约束条件\n\n2.设计方案：使用 UML+ARCHI 绘制数据结构和算法架构图\n\n3.实现步骤：自顶向下实现功能，遵循 Kubernetes 部署规范\n\n4.测试验证：编写单元测试（覆盖率 > 90%）和集成测试\n\n编程协作规则\n\n1.严格按用户指令执行，禁止添加未授权功能\n\n2.功能移植仅处理实际存在的代码，禁止模拟数据\n\n3.数据处理需源自真实数据源，禁止伪造逻辑\n\n4.代码修改需保留原始结构，新增依赖需提前审批\n\n5.重大修改前创建分支，修改后提供变更说明\n\n提交与版本控制\n\n・遵循语义化提交格式：feat:, fix:, docs: 等\n\n・主分支仅接受通过 SonarQube 分析（质量门 > 95 分）的代码\n\n・使用语义化版本管理，维护变更日志\n\n・关键功能更改必须通过代码审查和区块链存证\n\n数据库与配置管理\n\n数据库配置\n\n・确保.env 文件包含正确的数据库连接信息，格式如下：\n\n\nDB_HOST=localhostDB_PORT=5432DB_NAME=cursor_projectDB_USER=adminDB_PASSWORD=secure_passwordDB_SSL=true\n\n・敏感数据传输必须使用 TLS 1.3 加密，存储采用 AES256 加密\n\n・实现数据备份和恢复策略，遵循数据最小化原则\n\n动态配置管理\n\n・支持绝对 / 相对路径格式校验，配置文件以 AES256 加密存储\n\n・通过 Kubernetes ConfigMap 实现 500ms 内配置热更新\n\n・配置跨平台同步延迟 < 1s，支持版本回滚\n\n・配置文件变更需通过区块链智能合约验证\n\n测试与性能优化\n\n测试自动化规范\n\n・编写单元测试覆盖所有关键功能，使用 TDD 开发方法\n\n・实现端到端测试验证用户流程，测试数据与生产环境隔离\n\n・每次提交前运行自动化测试，集成测试覆盖率 > 90%\n\n・引擎切换测试：并发 1000 次无卡顿，持续运行 72 小时无异常\n\n性能优化指南\n\n・实现资源懒加载和按需加载，优化关键渲染路径\n\n・使用缓存策略减少网络请求，DOM 操作批量处理\n\n・线程池配置：CPU 密集型任务数 = 系统核心数 + 1，IO 密集型 = 核心数 ×2\n\n・内存操作采用共享内存映射，API 通信调用官方接口\n\n・定期进行性能审计，确保首屏加载速度 < 200ms\n\n安全与合规体系\n\n数据安全规范\n\n・敏感数据传输加密，用户输入验证和清洗\n\n・实现生物特征认证（指纹 + 人脸双重验证）\n\n・关键算法逻辑使用零知识证明（ZKP），操作日志 IPFS 存证\n\n・遵循证监会程序化交易监管规定，日志留存≥6 个月\n\n合规与异常处理\n\n・三级错误处理：预校验 + 运行时捕获 + 日志追踪\n\n・智能熔断机制响应延迟 < 5ms，故障恢复时间 < 10s\n\n・检测到违规操作时触发 LDAP 权限冻结和物理隔离\n\n・代码通过 SonarQube 分析，第三方依赖使用 Poetry 管理\n\n部署与发布流程\n\nCI/CD 流程\n\n・使用 Git Flow 分支策略（主分支 / 开发分支 / 特性分支）\n\n・实现自动化构建和部署，区分开发 / 测试 / 预发布 / 生产环境\n\n・版本发布生成数字签名证书，支持一键回滚\n\n・配置文件随环境变化自动切换，无需修改代码\n\n跨平台部署规范\n\n・线程框架通过 C++11 std::thread/Java ExecutorService 封装跨平台 API\n\n・使用 Docker 容器化部署，镜像基于阿里云仓库\n\n・动态调节线程优先级：THREAD_HIGH/THREAD_MED/THREAD_LOW\n\n・跨平台同步通过 Kafka 消息队列实现，状态切换延迟 < 800ms\n\n技术债务与可维护性\n\n代码维护原则\n\n・使用 TODO/FIXME 标记待优化点，定期清理技术债务\n\n・关键决策在代码中记录理由，避免黑魔法技巧\n\n・优先考虑代码可理解性，避免过早优化\n\n・建立团队培训和知识共享机制，定期进行代码审查\n\n版本控制与文档\n\n・维护项目架构图和关键流程图，记录技术选型理由\n\n・为 API 和数据模型提供详细文档，更新 README 反映项目状态\n\n・使用标准化问题报告模板，每个 bug 修复包含测试用例\n\n・文档体系包含开发者入门指南、已知问题解决方案\n\n依赖管理规范\n\n・优先使用国内镜像站点安装依赖：\n\n\n# npm/yarn/pnpm 镜像设置npm config set registry https://registry.npmmirror.comyarn config set registry https://registry.npmmirror.compnpm config set registry https://registry.npmmirror.com# pip 镜像设置pip install --trusted-host pypi.org --trusted-host files.pythonhosted.org -r requirements.txt# Maven/Gradle 镜像设置# 参考阿里云镜像文档配置\n\n・安装新依赖前验证国内可访问性，记录版本和来源\n\n・package.json 中添加镜像设置脚本，确保团队配置统一\n\n・禁止使用未通过安全审计的第三方依赖\n\n实施计划与风险控制\n\n阶段目标\n\n1.第 1 周：需求分析、Kubernetes 集群搭建、架构设计\n\n2.第 2-3 周：开发配置管理、引擎切换模块、跨平台线程框架\n\n3.第 4 周：系统集成与联调，单元测试覆盖率 > 90%\n\n4.第 5 周：72 小时稳定性压力测试，性能优化\n\n5.第 6 周：用户验收、文档交付、区块链存证归档\n\n风险控制措施\n\n・每日代码审查覆盖率 > 90%，版本发布需通过质量门\n\n・违规处理：检测到模拟数据时激活智能合约仲裁\n\n・建立反馈机制，持续优化开发流程\n\n・关键模块添加 \"锁定注释\"，防止未授权修改\n\n1.代码库：遵循所有规范，无模拟数据，通过硬件级调试验证\n\n2.部署包：支持 Kubernetes 一键部署，包含 DuiLib 原生 UI 组件\n\n3.文档集：架构设计、开发指南、测试报告、合规声明\n\n4.区块链存证：关键代码提交记录上链，确保不可篡改\n\n5.启动入口：单一主入口程序，禁止生成.bat/.cmd 文件",
  
  // Augment 网络连接增强配置
  "augment.completions.enableAutomaticCompletions": false,
  "augment.apiUrl": "https://i1.api.augmentcode.com",
  "augment.authUrl": "https://auth.augmentcode.com",
  "augment.allowInsecureConnections": true,
  "augment.network.timeout": 60000,
  "augment.network.retryAttempts": 10,
  "augment.network.retryDelay": 5000,
  "augment.network.useSystemProxy": false,

  // ========== Lingma AI 配置 ==========
  "Lingma.aI Chat.commandAllowlistInAgentMode": "cd,docker-compose,docker,python,echo,\"MULTI_AGENT_PORT=8008`nFEDERATED_LEARNING_PORT=8009`nBLOCKCHAIN_PORT=8010`nRL_AGENT_PORT=8007\",del,notepad,$env:ENVIRONMENT=\"development\",$env:ML_SERVICE_PORT=\"8005\",$env:RISK_SERVICE_PORT=\"8006\",$env:NOTIFICATION_SERVICE_PORT=\"8007\",$env:MULTI_AGENT_PORT=\"8008\",$env:FEDERATED_LEARNING_PORT=\"8009\",$env:BLOCKCHAIN_PORT=\"8010\",$env:RL_AGENT_PORT=\"8011\",findstr,Remove-Item,powershell,pip,uvicorn,Set-Location,dir,$env:PYTHONPATH=\".\",netstat,curl,Invoke-WebRequest,Get-ChildItem,cmd,sys.path.append('..'),from,import,uvicorn.run(app,",
  "Lingma.aI Chat.webToolsInAsk/AgentMode": "Auto-execute",

  // ========== 网络和代理配置 ==========
  "http.proxy": "",
  "https.proxy": "",
  "http.proxyStrictSSL": false,
  "http.proxySupport": "off",
  "http.systemCertificates": true,
  "http.userAgent": "Visual Studio Code",

  // ========== 网络代理绕过设置 ==========
  "http.proxyBypassList": [
    "localhost",
    "127.0.0.1",
    "*.augmentcode.com",
    "augmentcode.com", 
    "i0.api.augmentcode.com",
    "auth.augmentcode.com",
    "host.docker.internal"
  ],

  // ========== 信任的域名和 URL 白名单 ==========
  "security.allowedUNCHosts": [
    "auth.augmentcode.com",
    "i1.api.augmentcode.com",
    "api.augmentcode.com",
    "augmentcode.com",
    "*.augmentcode.com"
  ],

  "workbench.trustedDomains": [
    "https://auth.augmentcode.com",
    "https://i1.api.augmentcode.com",
    "https://api.augmentcode.com",
    "https://*.augmentcode.com",
    "*.augmentcode.com",
    "vscode://augment.vscode-augment"
  ],

  // ========== 安全策略 ==========
  "security.workspace.trust.untrustedFiles": "open",
  "security.workspace.trust.banner": "never",
  "security.workspace.trust.startupPrompt": "never",
  "security.workspace.trust.enabled": false,

  // ========== 扩展配置 ==========
  "extensions.autoCheckUpdates": true,
  "extensions.autoUpdate": true,
  "extensions.closeExtensionDetailsOnViewChange": true,
  "extensions.ignoreRecommendations": false,
  "extensions.webWorker": true,
  
  "extensions.supportVirtualWorkspaces": {
    "augment.vscode-augment": true
  },

  "extensions.galleryApiUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",
  "extensions.serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",

  // ========== 远程连接设置 ==========
  "remote.extensionKind": {
    "augment.vscode-augment": ["ui", "workspace"]
  },
  "remote.SSH.connectTimeout": 60,
  "remote.SSH.remoteServerListenOnSocket": true,

  // ========== 其他系统设置 ==========
  "telemetry.telemetryLevel": "error",
  "git.ignoreLegacyWarning": true,
  "update.mode": "start"
}